<div class="container-fluid px-4 py-6">
  <!-- Header -->
  <div class="row mb-6">
    <div class="col-12">
      <div class="d-flex align-items-center justify-content-between">
        <div>
          <h1 class="text-dark-blue fw-bold mb-2">
            <i class="fas fa-chart-bar me-3"></i>
            البيانات العامة للنظام
          </h1>
          <p class="text-muted mb-0">إحصائيات شاملة للوحدات والإعلانات والأسعار</p>
        </div>
        <button class="btn btn-light-primary" (click)="loadGeneralStatistics()">
          <i class="fas fa-sync-alt me-2"></i>
          تحديث البيانات
        </button>
      </div>
    </div>
  </div>

  <!-- Main Statistics Cards -->
  <div class="row g-6 mb-8">
    <div class="col-lg-3 col-md-6">
      <div class="card card-flush h-100 bg-dark-blue">
        <div class="card-body text-white">
          <div class="d-flex align-items-center">
            <div class="symbol symbol-50px me-3">
              <div class="symbol-label bg-white bg-opacity-20">
                <i class="fas fa-building fs-2 text-white"></i>
              </div>
            </div>
            <div>
              <div class="fs-2 fw-bold">{{ formatNumber(totalUnits) }}</div>
              <div class="fs-7 text-white-75">إجمالي الوحدات</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-3 col-md-6">
      <div class="card card-flush h-100 bg-primary">
        <div class="card-body text-white">
          <div class="d-flex align-items-center">
            <div class="symbol symbol-50px me-3">
              <div class="symbol-label bg-white bg-opacity-20">
                <i class="fas fa-bullhorn fs-2 text-white"></i>
              </div>
            </div>
            <div>
              <div class="fs-2 fw-bold">{{ formatNumber(totalAds) }}</div>
              <div class="fs-7 text-white-75">إجمالي الإعلانات</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-3 col-md-6">
      <div class="card card-flush h-100 bg-mid-blue">
        <div class="card-body text-white">
          <div class="d-flex align-items-center">
            <div class="symbol symbol-50px me-3">
              <div class="symbol-label bg-white bg-opacity-20">
                <i class="fas fa-money-bill-wave fs-2 text-white"></i>
              </div>
            </div>
            <div>
              <div class="fs-2 fw-bold">{{ formatPrice(averagePrice) }}</div>
              <div class="fs-7 text-white-75">متوسط السعر</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-3 col-md-6">
      <div class="card card-flush h-100 bg-success">
        <div class="card-body text-white">
          <div class="d-flex align-items-center">
            <div class="symbol symbol-50px me-3">
              <div class="symbol-label bg-white bg-opacity-20">
                <i class="fas fa-eye fs-2 text-white"></i>
              </div>
            </div>
            <div>
              <div class="fs-2 fw-bold">{{ formatNumber(totalViews) }}</div>
              <div class="fs-7 text-white-75">إجمالي المشاهدات</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Charts and Statistics Row -->
  <div class="row g-6 mb-8">
    <!-- Most Demanded by Area -->
    <div class="col-lg-6">
      <div class="card card-flush h-100">
        <div class="card-header border-0 pt-6">
          <h3 class="card-title text-dark-blue fw-bold">
            <i class="fas fa-map-marker-alt me-2"></i>
            الوحدات الأكثر طلباً حسب المنطقة
          </h3>
        </div>
        <div class="card-body pt-0">
          <div class="table-responsive">
            <table class="table table-row-bordered align-middle gs-0 gy-3">
              <thead>
                <tr class="fw-bold text-muted bg-light">
                  <th class="ps-4 rounded-start">المنطقة</th>
                  <th>عدد الطلبات</th>
                  <th class="rounded-end">النسبة</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let area of mostDemandedByArea">
                  <td class="ps-4">
                    <span class="text-dark fw-bold">{{ area.area }}</span>
                  </td>
                  <td>
                    <span class="badge badge-light-primary">{{ area.count }}</span>
                  </td>
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="progress h-6px w-100px me-2">
                        <div class="progress-bar bg-primary" [style.width.%]="area.percentage"></div>
                      </div>
                      <span class="text-muted fs-7">{{ area.percentage }}%</span>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Most Demanded by Type -->
    <div class="col-lg-6">
      <div class="card card-flush h-100">
        <div class="card-header border-0 pt-6">
          <h3 class="card-title text-dark-blue fw-bold">
            <i class="fas fa-home me-2"></i>
            الوحدات الأكثر طلباً حسب النوع
          </h3>
        </div>
        <div class="card-body pt-0">
          <div class="table-responsive">
            <table class="table table-row-bordered align-middle gs-0 gy-3">
              <thead>
                <tr class="fw-bold text-muted bg-light">
                  <th class="ps-4 rounded-start">نوع الوحدة</th>
                  <th>عدد الطلبات</th>
                  <th class="rounded-end">النسبة</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let type of mostDemandedByType">
                  <td class="ps-4">
                    <span class="text-dark fw-bold">{{ type.type }}</span>
                  </td>
                  <td>
                    <span class="badge badge-light-success">{{ type.count }}</span>
                  </td>
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="progress h-6px w-100px me-2">
                        <div class="progress-bar bg-success" [style.width.%]="type.percentage"></div>
                      </div>
                      <span class="text-muted fs-7">{{ type.percentage }}%</span>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Best Selling Units -->
  <div class="row g-6 mb-8">
    <div class="col-12">
      <div class="card card-flush">
        <div class="card-header border-0 pt-6">
          <h3 class="card-title text-dark-blue fw-bold">
            <i class="fas fa-trophy me-2"></i>
            الوحدات الأكثر مبيعاً
          </h3>
        </div>
        <div class="card-body pt-0">
          <div class="table-responsive">
            <table class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3">
              <thead>
                <tr class="fw-bold text-muted bg-light">
                  <th class="ps-4 rounded-start">الوحدة</th>
                  <th>السعر</th>
                  <th>المنطقة</th>
                  <th>النوع</th>
                  <th>عدد المبيعات</th>
                  <th class="rounded-end">المطور</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let unit of bestSellingUnits">
                  <td class="ps-4">
                    <span class="text-dark fw-bold">{{ unit.title }}</span>
                  </td>
                  <td>
                    <span class="text-primary fw-bold">{{ formatPrice(unit.price) }}</span>
                  </td>
                  <td>
                    <span class="text-muted">{{ unit.area }}</span>
                  </td>
                  <td>
                    <span class="badge badge-light-info">{{ unit.type }}</span>
                  </td>
                  <td>
                    <span class="badge badge-light-success">{{ unit.sales }}</span>
                  </td>
                  <td>
                    <span class="text-muted">{{ unit.developer }}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Price Statistics -->
  <div class="row g-6">
    <div class="col-lg-6">
      <div class="card card-flush h-100">
        <div class="card-header border-0 pt-6">
          <h3 class="card-title text-dark-blue fw-bold">
            <i class="fas fa-chart-line me-2"></i>
            إحصائيات الأسعار
          </h3>
        </div>
        <div class="card-body pt-0">
          <div class="row g-4">
            <div class="col-6">
              <div class="bg-light-primary p-4 rounded">
                <div class="text-primary fs-6 fw-bold">متوسط سعر البيع</div>
                <div class="text-dark fs-3 fw-bolder">{{ formatPrice(priceStatistics.averageSalePrice) }}</div>
              </div>
            </div>
            <div class="col-6">
              <div class="bg-light-success p-4 rounded">
                <div class="text-success fs-6 fw-bold">متوسط سعر الإيجار</div>
                <div class="text-dark fs-3 fw-bolder">{{ formatPrice(priceStatistics.averageRentPrice) }}</div>
              </div>
            </div>
            <div class="col-6">
              <div class="bg-light-warning p-4 rounded">
                <div class="text-warning fs-6 fw-bold">أعلى سعر</div>
                <div class="text-dark fs-3 fw-bolder">{{ formatPrice(priceStatistics.highestPrice) }}</div>
              </div>
            </div>
            <div class="col-6">
              <div class="bg-light-info p-4 rounded">
                <div class="text-info fs-6 fw-bold">أقل سعر</div>
                <div class="text-dark fs-3 fw-bolder">{{ formatPrice(priceStatistics.lowestPrice) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Area Statistics -->
    <div class="col-lg-6">
      <div class="card card-flush h-100">
        <div class="card-header border-0 pt-6">
          <h3 class="card-title text-dark-blue fw-bold">
            <i class="fas fa-map me-2"></i>
            إحصائيات المناطق
          </h3>
        </div>
        <div class="card-body pt-0">
          <div class="table-responsive">
            <table class="table table-row-bordered align-middle gs-0 gy-3">
              <thead>
                <tr class="fw-bold text-muted bg-light">
                  <th class="ps-4 rounded-start">المنطقة</th>
                  <th>الوحدات</th>
                  <th>متوسط السعر</th>
                  <th class="rounded-end">النمو</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let area of areaStatistics">
                  <td class="ps-4">
                    <span class="text-dark fw-bold">{{ area.name }}</span>
                  </td>
                  <td>
                    <span class="text-muted">{{ area.units }}</span>
                  </td>
                  <td>
                    <span class="text-primary fw-bold">{{ formatPrice(area.avgPrice) }}</span>
                  </td>
                  <td>
                    <span class="badge"
                      [ngClass]="area.growth > 10 ? 'badge-light-success' : area.growth > 5 ? 'badge-light-warning' : 'badge-light-danger'">
                      {{ area.growth }}%
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>