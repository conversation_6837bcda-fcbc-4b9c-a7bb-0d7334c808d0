<div class="container-fluid px-4 py-6">
  <!-- Header -->
  <div class="row mb-6">
    <div class="col-12">
      <div class="d-flex align-items-center justify-content-between">
        <div>
          <h1 class="text-dark-blue fw-bold mb-2">
            <i class="fas fa-handshake me-3"></i>
            بيانات الوسطاء
          </h1>
          <p class="text-muted mb-0">إحصائيات شاملة للوسطاء والعمولات والمعاملات</p>
        </div>
        <button class="btn btn-light-primary" (click)="loadBrokerStatistics()">
          <i class="fas fa-sync-alt me-2"></i>
          تحديث البيانات
        </button>
      </div>
    </div>
  </div>

  <!-- Main Statistics Cards -->
  <div class="row g-6 mb-8">
    <div class="col-lg-3 col-md-6">
      <div class="card card-flush h-100" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
        <div class="card-body text-white">
          <div class="d-flex align-items-center">
            <div class="symbol symbol-50px me-3">
              <div class="symbol-label bg-white bg-opacity-20">
                <i class="fas fa-users fs-2 text-white"></i>
              </div>
            </div>
            <div>
              <div class="fs-2 fw-bold">{{ formatNumber(totalBrokers) }}</div>
              <div class="fs-7 text-white-75">إجمالي الوسطاء</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-3 col-md-6">
      <div class="card card-flush h-100" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div class="card-body text-white">
          <div class="d-flex align-items-center">
            <div class="symbol symbol-50px me-3">
              <div class="symbol-label bg-white bg-opacity-20">
                <i class="fas fa-user-check fs-2 text-white"></i>
              </div>
            </div>
            <div>
              <div class="fs-2 fw-bold">{{ formatNumber(activeBrokers) }}</div>
              <div class="fs-7 text-white-75">الوسطاء النشطين</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-3 col-md-6">
      <div class="card card-flush h-100" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
        <div class="card-body text-white">
          <div class="d-flex align-items-center">
            <div class="symbol symbol-50px me-3">
              <div class="symbol-label bg-white bg-opacity-20">
                <i class="fas fa-money-bill-wave fs-2 text-white"></i>
              </div>
            </div>
            <div>
              <div class="fs-2 fw-bold">{{ formatPrice(totalCommissions) }}</div>
              <div class="fs-7 text-white-75">إجمالي العمولات</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-3 col-md-6">
      <div class="card card-flush h-100" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
        <div class="card-body text-white">
          <div class="d-flex align-items-center">
            <div class="symbol symbol-50px me-3">
              <div class="symbol-label bg-white bg-opacity-20">
                <i class="fas fa-percentage fs-2 text-white"></i>
              </div>
            </div>
            <div>
              <div class="fs-2 fw-bold">{{ averageCommissionRate }}%</div>
              <div class="fs-7 text-white-75">متوسط نسبة العمولة</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Top Brokers and Revenue Stats -->
  <div class="row g-6 mb-8">
    <!-- Top Brokers -->
    <div class="col-lg-8">
      <div class="card card-flush h-100">
        <div class="card-header border-0 pt-6">
          <h3 class="card-title text-dark-blue fw-bold">
            <i class="fas fa-trophy me-2"></i>
            أفضل الوسطاء
          </h3>
        </div>
        <div class="card-body pt-0">
          <div class="table-responsive">
            <table class="table table-row-bordered align-middle gs-0 gy-3">
              <thead>
                <tr class="fw-bold text-muted bg-light">
                  <th class="ps-4 rounded-start">الوسيط</th>
                  <th>المعاملات</th>
                  <th>العمولات</th>
                  <th>التخصص</th>
                  <th class="rounded-end">التقييم</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let broker of topBrokers; let i = index">
                  <td class="ps-4">
                    <div class="d-flex align-items-center">
                      <div class="symbol symbol-40px me-3">
                        <div class="symbol-label bg-light-primary">
                          <span class="text-primary fw-bold">{{ i + 1 }}</span>
                        </div>
                      </div>
                      <div>
                        <span class="text-dark fw-bold d-block">{{ broker.name }}</span>
                        <span class="text-muted fs-7">{{ broker.phone }}</span>
                      </div>
                    </div>
                  </td>
                  <td>
                    <span class="badge badge-light-primary">{{ broker.transactions }}</span>
                  </td>
                  <td>
                    <span class="text-success fw-bold">{{ formatPrice(broker.totalCommissions) }}</span>
                  </td>
                  <td>
                    <span class="badge badge-light-info">{{ broker.specialization }}</span>
                  </td>
                  <td>
                    <div class="d-flex align-items-center">
                      <span class="me-2">{{ broker.rating }}</span>
                      <div class="rating">
                        <i *ngFor="let star of getRatingStars(broker.rating)" [class]="star"></i>
                      </div>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Revenue Statistics -->
    <div class="col-lg-4">
      <div class="card card-flush h-100">
        <div class="card-header border-0 pt-6">
          <h3 class="card-title text-dark-blue fw-bold">
            <i class="fas fa-chart-line me-2"></i>
            إحصائيات الإيرادات
          </h3>
        </div>
        <div class="card-body pt-0">
          <div class="row g-4">
            <div class="col-12">
              <div class="bg-light-success p-4 rounded">
                <div class="text-success fs-6 fw-bold">إجمالي الإيرادات</div>
                <div class="text-dark fs-3 fw-bolder">{{ formatPrice(revenueStats.totalRevenue) }}</div>
              </div>
            </div>
            <div class="col-12">
              <div class="bg-light-primary p-4 rounded">
                <div class="text-primary fs-6 fw-bold">الإيرادات الشهرية</div>
                <div class="text-dark fs-3 fw-bolder">{{ formatPrice(revenueStats.monthlyRevenue) }}</div>
              </div>
            </div>
            <div class="col-12">
              <div class="bg-light-warning p-4 rounded">
                <div class="text-warning fs-6 fw-bold">متوسط قيمة المعاملة</div>
                <div class="text-dark fs-3 fw-bolder">{{ formatPrice(revenueStats.averageTransactionValue) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Commission Stats and Transaction Stats -->
  <div class="row g-6 mb-8">
    <!-- Commission Statistics -->
    <div class="col-lg-6">
      <div class="card card-flush h-100">
        <div class="card-header border-0 pt-6">
          <h3 class="card-title text-dark-blue fw-bold">
            <i class="fas fa-percent me-2"></i>
            إحصائيات العمولات
          </h3>
        </div>
        <div class="card-body pt-0">
          <div class="table-responsive">
            <table class="table table-row-bordered align-middle gs-0 gy-3">
              <thead>
                <tr class="fw-bold text-muted bg-light">
                  <th class="ps-4 rounded-start">نطاق العمولة</th>
                  <th>عدد الوسطاء</th>
                  <th class="rounded-end">النسبة</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let commission of commissionStats">
                  <td class="ps-4">
                    <span class="text-dark fw-bold">{{ commission.range }}</span>
                  </td>
                  <td>
                    <span class="badge badge-light-primary">{{ commission.count }}</span>
                  </td>
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="progress h-6px w-100px me-2">
                        <div class="progress-bar bg-primary" [style.width.%]="commission.percentage"></div>
                      </div>
                      <span class="text-muted fs-7">{{ commission.percentage }}%</span>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Transaction Statistics -->
    <div class="col-lg-6">
      <div class="card card-flush h-100">
        <div class="card-header border-0 pt-6">
          <h3 class="card-title text-dark-blue fw-bold">
            <i class="fas fa-exchange-alt me-2"></i>
            إحصائيات المعاملات
          </h3>
        </div>
        <div class="card-body pt-0">
          <div class="row g-4">
            <div class="col-6">
              <div class="bg-light-primary p-4 rounded text-center">
                <div class="text-primary fs-6 fw-bold">إجمالي المعاملات</div>
                <div class="text-dark fs-2 fw-bolder">{{ formatNumber(transactionStats.totalTransactions) }}</div>
              </div>
            </div>
            <div class="col-6">
              <div class="bg-light-success p-4 rounded text-center">
                <div class="text-success fs-6 fw-bold">مكتملة</div>
                <div class="text-dark fs-2 fw-bolder">{{ formatNumber(transactionStats.completedTransactions) }}</div>
              </div>
            </div>
            <div class="col-6">
              <div class="bg-light-warning p-4 rounded text-center">
                <div class="text-warning fs-6 fw-bold">قيد الانتظار</div>
                <div class="text-dark fs-2 fw-bolder">{{ formatNumber(transactionStats.pendingTransactions) }}</div>
              </div>
            </div>
            <div class="col-6">
              <div class="bg-light-danger p-4 rounded text-center">
                <div class="text-danger fs-6 fw-bold">ملغية</div>
                <div class="text-dark fs-2 fw-bolder">{{ formatNumber(transactionStats.cancelledTransactions) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Specialization Stats and Monthly Performance -->
  <div class="row g-6 mb-8">
    <!-- Specialization Statistics -->
    <div class="col-lg-6">
      <div class="card card-flush h-100">
        <div class="card-header border-0 pt-6">
          <h3 class="card-title text-dark-blue fw-bold">
            <i class="fas fa-tags me-2"></i>
            إحصائيات التخصصات
          </h3>
        </div>
        <div class="card-body pt-0">
          <div class="table-responsive">
            <table class="table table-row-bordered align-middle gs-0 gy-3">
              <thead>
                <tr class="fw-bold text-muted bg-light">
                  <th class="ps-4 rounded-start">التخصص</th>
                  <th>عدد الوسطاء</th>
                  <th class="rounded-end">النسبة</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let spec of specializationStats">
                  <td class="ps-4">
                    <span class="text-dark fw-bold">{{ spec.specialization }}</span>
                  </td>
                  <td>
                    <span class="badge badge-light-success">{{ spec.brokers }}</span>
                  </td>
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="progress h-6px w-100px me-2">
                        <div class="progress-bar bg-success" [style.width.%]="spec.percentage"></div>
                      </div>
                      <span class="text-muted fs-7">{{ spec.percentage }}%</span>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Monthly Performance -->
    <div class="col-lg-6">
      <div class="card card-flush h-100">
        <div class="card-header border-0 pt-6">
          <h3 class="card-title text-dark-blue fw-bold">
            <i class="fas fa-calendar-alt me-2"></i>
            الأداء الشهري
          </h3>
        </div>
        <div class="card-body pt-0">
          <div class="table-responsive">
            <table class="table table-row-bordered align-middle gs-0 gy-3">
              <thead>
                <tr class="fw-bold text-muted bg-light">
                  <th class="ps-4 rounded-start">الشهر</th>
                  <th>المعاملات</th>
                  <th class="rounded-end">العمولات</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let month of monthlyPerformance">
                  <td class="ps-4">
                    <span class="text-dark fw-bold">{{ month.month }}</span>
                  </td>
                  <td>
                    <span class="badge badge-light-primary">{{ month.transactions }}</span>
                  </td>
                  <td>
                    <span class="text-success fw-bold">{{ formatPrice(month.commissions) }}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Broker Performance -->
  <div class="row g-6">
    <div class="col-12">
      <div class="card card-flush">
        <div class="card-header border-0 pt-6">
          <h3 class="card-title text-dark-blue fw-bold">
            <i class="fas fa-chart-bar me-2"></i>
            أداء الوسطاء
          </h3>
        </div>
        <div class="card-body pt-0">
          <div class="table-responsive">
            <table class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3">
              <thead>
                <tr class="fw-bold text-muted bg-light">
                  <th class="ps-4 rounded-start">الوسيط</th>
                  <th>وقت الاستجابة</th>
                  <th>رضا العملاء</th>
                  <th>معدل إتمام الصفقات</th>
                  <th class="rounded-end">معدل المتابعة</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let performance of brokerPerformance">
                  <td class="ps-4">
                    <span class="text-dark fw-bold">{{ performance.name }}</span>
                  </td>
                  <td>
                    <span class="badge" 
                          [ngClass]="'badge-light-' + getResponseTimeColor(performance.responseTime)">
                      {{ performance.responseTime }} دقيقة
                    </span>
                  </td>
                  <td>
                    <div class="d-flex align-items-center">
                      <span class="me-2">{{ performance.clientSatisfaction }}</span>
                      <div class="rating">
                        <i *ngFor="let star of getRatingStars(performance.clientSatisfaction)" [class]="star"></i>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="progress h-6px w-100px me-2">
                        <div class="progress-bar" 
                             [ngClass]="'bg-' + getPerformanceColor(performance.dealClosureRate)"
                             [style.width.%]="performance.dealClosureRate"></div>
                      </div>
                      <span class="text-muted fs-7">{{ performance.dealClosureRate }}%</span>
                    </div>
                  </td>
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="progress h-6px w-100px me-2">
                        <div class="progress-bar" 
                             [ngClass]="'bg-' + getPerformanceColor(performance.followUpRate)"
                             [style.width.%]="performance.followUpRate"></div>
                      </div>
                      <span class="text-muted fs-7">{{ performance.followUpRate }}%</span>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
