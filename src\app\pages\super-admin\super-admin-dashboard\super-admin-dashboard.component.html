<!-- Dashboard Navigation Cards -->
<div class="row mb-8">
  <div class="col-12">
    <h2 class="text-dark-blue fw-bold mb-6">
      <i class="fas fa-chart-line me-3"></i>
      Dashboard Control Panel
    </h2>
  </div>
</div>

<div class="row g-6 mb-8">
  <!-- General Data Dashboard Card -->
  <div class="col-lg-3 col-md-6">
    <div class="card card-flush h-100 cursor-pointer dashboard-nav-card" (click)="navigateToDashboard('general')"
      style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
      <div class="card-body d-flex flex-column justify-content-between text-white">
        <div class="d-flex align-items-center mb-4">
          <div class="symbol symbol-50px me-3">
            <div class="symbol-label bg-white bg-opacity-20">
              <i class="fas fa-chart-bar fs-2 text-white"></i>
            </div>
          </div>
          <div>
            <h4 class="text-white fw-bold mb-1"> General Data Dashboard </h4>
            <p class="text-white-75 mb-0 fs-7"> General statistics for the system </p>
          </div>
        </div>

        <div class="row g-3">
          <div class="col-6">
            <div class="bg-white bg-opacity-20 rounded p-3 text-center">
              <div class="fs-6 fw-bold">{{ totalUnits }}</div>
              <div class="fs-8 text-white-75"> Total Units </div>
            </div>
          </div>
          <div class="col-6">
            <div class="bg-white bg-opacity-20 rounded p-3 text-center">
              <div class="fs-6 fw-bold">{{ totalAds }}</div>
              <div class="fs-8 text-white-75"> Total Ads </div>
            </div>
          </div>
        </div>

        <div class="d-flex justify-content-between align-items-center mt-4">
          <span class="fs-7 text-white-75"> View Details </span>
          <i class="fas fa-arrow-right text-white"></i>
        </div>
      </div>
    </div>
  </div>

  <!-- Developers Dashboard Card -->
  <div class="col-lg-3 col-md-6">
    <div class="card card-flush h-100 cursor-pointer dashboard-nav-card" (click)="navigateToDashboard('developers')"
      style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
      <div class="card-body d-flex flex-column justify-content-between text-white">
        <div class="d-flex align-items-center mb-4">
          <div class="symbol symbol-50px me-3">
            <div class="symbol-label bg-white bg-opacity-20">
              <i class="fas fa-hard-hat fs-2 text-white"></i>
            </div>
          </div>
          <div>
            <h4 class="text-white fw-bold mb-1"> Developers Dashboard </h4>
            <p class="text-white-75 mb-0 fs-7"> Statistics for developers and projects
            </p>
          </div>
        </div>

        <div class="row g-3">
          <div class="col-6">
            <div class="bg-white bg-opacity-20 rounded p-3 text-center">
              <div class="fs-6 fw-bold">{{ totalDevelopers }}</div>
              <div class="fs-8 text-white-75"> Total Developers </div>
            </div>
          </div>
          <div class="col-6">
            <div class="bg-white bg-opacity-20 rounded p-3 text-center">
              <div class="fs-6 fw-bold">{{ totalProjects }}</div>
              <div class="fs-8 text-white-75"> Total Projects </div>
            </div>
          </div>
        </div>

        <div class="d-flex justify-content-between align-items-center mt-4">
          <span class="fs-7 text-white-75"> View Details </span>
          <i class="fas fa-arrow-right text-white"></i>
        </div>
      </div>
    </div>
  </div>

  <!-- Brokers Dashboard Card -->
  <div class="col-lg-3 col-md-6">
    <div class="card card-flush h-100 cursor-pointer dashboard-nav-card" (click)="navigateToDashboard('brokers')"
      style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
      <div class="card-body d-flex flex-column justify-content-between text-white">
        <div class="d-flex align-items-center mb-4">
          <div class="symbol symbol-50px me-3">
            <div class="symbol-label bg-white bg-opacity-20">
              <i class="fas fa-handshake fs-2 text-white"></i>
            </div>
          </div>
          <div>
            <h4 class="text-white fw-bold mb-1"> Brokers Dashboard </h4>
            <p class="text-white-75 mb-0 fs-7"> Statistics for brokers and commissions
            </p>
          </div>
        </div>

        <div class="row g-3">
          <div class="col-6">
            <div class="bg-white bg-opacity-20 rounded p-3 text-center">
              <div class="fs-6 fw-bold">{{ totalBrokers }}</div>
              <div class="fs-8 text-white-75"> Total Brokers </div>
            </div>
          </div>
          <div class="col-6">
            <div class="bg-white bg-opacity-20 rounded p-3 text-center">
              <div class="fs-6 fw-bold">{{ activeBrokers }}</div>
              <div class="fs-8 text-white-75"> Active Brokers </div>
            </div>
          </div>
        </div>

        <div class="d-flex justify-content-between align-items-center mt-4">
          <span class="fs-7 text-white-75"> View Details </span>
          <i class="fas fa-arrow-right text-white"></i>
        </div>
      </div>
    </div>
  </div>

  <!-- Clients Dashboard Card -->
  <div class="col-lg-3 col-md-6">
    <div class="card card-flush h-100 cursor-pointer dashboard-nav-card" (click)="navigateToDashboard('clients')"
      style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
      <div class="card-body d-flex flex-column justify-content-between text-white">
        <div class="d-flex align-items-center mb-4">
          <div class="symbol symbol-50px me-3">
            <div class="symbol-label bg-white bg-opacity-20">
              <i class="fas fa-users fs-2 text-white"></i>
            </div>
          </div>
          <div>
            <h4 class="text-white fw-bold mb-1"> Clients Dashboard </h4>
            <p class="text-white-75 mb-0 fs-7"> Statistics for clients and requests
            </p>
          </div>
        </div>

        <div class="row g-3">
          <div class="col-6">
            <div class="bg-white bg-opacity-20 rounded p-3 text-center">
              <div class="fs-6 fw-bold">{{ totalClients }}</div>
              <div class="fs-8 text-white-75"> Total Clients </div>
            </div>
          </div>
          <div class="col-6">
            <div class="bg-white bg-opacity-20 rounded p-3 text-center">
              <div class="fs-6 fw-bold">{{ activeClients }}</div>
              <div class="fs-8 text-white-75"> Active Clients </div>
            </div>
          </div>
        </div>

        <div class="d-flex justify-content-between align-items-center mt-4">
          <span class="fs-7 text-white-75"> View Details </span>
          <i class="fas fa-arrow-right text-white"></i>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Original Statistics Section -->
<div class="row mb-8">
  <div class="col-12">
    <h3 class="text-dark-blue fw-bold mb-6">
      <i class="fas fa-tachometer-alt me-3"></i>
      Quick Statistics
    </h3>
  </div>
</div>

<div class="row">
  <div class="col-md-3">
    <app-analysis-card [backgroundColor]="'warning'" [title]="'Total Users'" [totalRequests]="totalUsers"
      [activeRequests]="totalDevelopers"></app-analysis-card>
    <app-analysis-card [backgroundColor]="'success'" [title]="'Developers'" [totalRequests]="totalUsers"
      [activeRequests]="totalDevelopers"></app-analysis-card>
  </div>

  <div class="col-md-3">
    <app-analysis-card [backgroundColor]="'mid-blue'" [title]="'Brokers'" [totalRequests]="totalUsers"
      [activeRequests]="totalBrokers"></app-analysis-card>
    <app-analysis-card [backgroundColor]="'dark-blue'" [title]="'Clients'" [totalRequests]="totalUsers"
      [activeRequests]="totalClients"></app-analysis-card>
  </div>

  <div class="col-md-3">
    <app-analysis-card [backgroundColor]="'light-dark-blue'" [title]="'Projects'" [totalRequests]="totalProjects"
      [activeRequests]="projectStats.active"></app-analysis-card>
    <app-analysis-card [backgroundColor]="'danger'" [title]="'Contracts'" [totalRequests]="totalContracts"
      [activeRequests]="contractStats.accepted"></app-analysis-card>
  </div>

  <div class="col-md-3">
    <div class="dashboard-pie-chart">
      <div class="pie-chart-section">
        <app-project-pie-chart [newCount]="userStats.developers" [availableCount]="userStats.brokers"
          [soldCount]="userStats.clients" [reservedCount]="userStats.admins">
        </app-project-pie-chart>
      </div>

      <div class="mt-3">
        <app-contract-requests-chart [cssClass]="'mb-5'" [chartSize]="70" [chartLine]="11" [chartRotate]="145"
          [pending]="contractStats.pending" [accepted]="contractStats.accepted" [declined]="contractStats.declined">
        </app-contract-requests-chart>
      </div>
    </div>
  </div>
</div>

<!-- Recent activities -->
<div class="card mt-5">
  <div class="card-header border-0 pt-5">
    <h3 class="card-title align-items-start flex-column">
      <span class="card-label fw-bolder fs-3 mb-1 text-dark-blue">
        <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g clip-path="url(#clip0_24_2533)">
            <path stroke="#0D47A1" stroke-width="1"
              d="M6.53125 10.0938C7.02313 10.0938 7.42188 9.695 7.42188 9.20312C7.42188 8.71125 7.02313 8.3125 6.53125 8.3125C6.03937 8.3125 5.64062 8.71125 5.64062 9.20312C5.64062 9.695 6.03937 10.0938 6.53125 10.0938Z" />
            <path stroke="#0D47A1" stroke-width="1"
              d="M12.4688 10.0938C12.9606 10.0938 13.3594 9.695 13.3594 9.20312C13.3594 8.71125 12.9606 8.3125 12.4688 8.3125C11.9769 8.3125 11.5781 8.71125 11.5781 9.20312C11.5781 9.695 11.9769 10.0938 12.4688 10.0938Z" />
            <path stroke="#0D47A1" stroke-width="1"
              d="M17.8125 4.75H16.625V3.5625C16.625 3.07062 16.2262 2.67188 15.7344 2.67188C15.2425 2.67188 14.8438 3.07062 14.8438 3.5625V4.75H4.15625V3.5625C4.15625 3.07062 3.75751 2.67188 3.26562 2.67188C2.77374 2.67188 2.375 3.07062 2.375 3.5625V4.75H1.1875C0.695625 4.75 0.296875 5.14875 0.296875 5.64062V16.625C0.296875 17.1169 0.695625 17.5156 1.1875 17.5156H17.8125C18.3044 17.5156 18.7031 17.1169 18.7031 16.625V5.64062C18.7031 5.14875 18.3044 4.75 17.8125 4.75ZM16.9219 15.7344H2.07812V6.53125H16.9219V15.7344Z" />
          </g>
          <defs>
            <clipPath id="clip0_24_2533">
              <rect width="19" height="19" fill="white" />
            </clipPath>
          </defs>
        </svg>
        Recent System Activities
      </span>
      <span class="text-danger mt-1 fw-bold fs-7">
        You have {{ recentActivities.length }} recent activities
      </span>
    </h3>

    <div class="card-toolbar">
      <a class="btn btn-sm btn-dark-blue btn-active-light-dark-blue">
        View all
        <i class="fa-solid fa-angles-right fs-7"></i>
      </a>
    </div>
  </div>

  <div class="card-body py-3">
    <div class="table-responsive">
      <table class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3">
        <thead>
          <tr class="fw-bolder bg-light-dark-blue text-dark-blue me-1 ms-1">
            <th class="min-w-120px ps-4 rounded-start">Activity Type</th>
            <th class="min-w-120px">Description</th>
            <th class="min-w-120px">User</th>
            <th class="min-w-120px">Date</th>
            <th class="min-w-120px rounded-end">Status</th>
          </tr>
        </thead>

        <tbody>
          <tr *ngFor="let activity of recentActivities">
            <td>
              <a class="text-gray-800 fw-semibold text-hover-primary d-block mb-1 fs-5 cursor-pointer">
                {{ activity?.type || 'System Activity' }}
              </a>
            </td>
            <td>
              <span class="text-gray-800 fw-semibold fs-5">
                {{ activity?.message || 'Activity description' }}
              </span>
            </td>
            <td>
              <span class="text-gray-800 fw-semibold fs-5">
                {{ activity?.user || 'System' }}
              </span>
            </td>
            <td>
              <span class="text-gray-800 fw-semibold d-block mb-1 fs-5">
                {{ activity?.timestamp | date:'short' }}
              </span>
            </td>
            <td>
              <span class="fw-bold badge fs-5 fw-semibold badge-light-success">
                Active
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>