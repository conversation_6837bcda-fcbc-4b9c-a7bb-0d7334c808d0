.dashboard-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.dashboard-content {
  padding: 20px;
  overflow-y: auto;
}

.dashboard-cards-container {
  margin-top: 20px;
}

.project-title,
.project-title-left {
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    opacity: 0.8;
  }
}

.project-title-left {
  .text-dark {
    font-size: 1rem;
  }

  i {
    font-size: 1.1rem;
  }
}

.main-dashboard-container {
  display: flex;
  flex-direction: row;
  gap: 1px;
}

.analysis-cards-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1px;
  flex: 55%;
}

.card {
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
  border: none;

  &.bg-primary,
  &.bg-success,
  &.bg-danger,
  &.bg-warning {
    .card-header {
      border-bottom: none;
    }
  }
}

.dashboard-pie-chart {
  height: 100%;
  display: flex;
  flex-direction: column;

  .pie-chart-section {
    flex: 1;
  }
}

// Dashboard navigation cards styles
.dashboard-nav-card {
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  }

  .card-body {
    padding: 2rem;
    min-height: 200px;
  }

  .symbol-label {
    border-radius: 12px;
  }

  .bg-white.bg-opacity-20 {
    backdrop-filter: blur(10px);
    border-radius: 8px;
  }
}

.text-white-75 {
  color: rgba(255, 255, 255, 0.75) !important;
}

// Dashboard Header
.dashboard-header {
  border: 1px solid #E4E6EF;

  .symbol-label {
    border-radius: 12px;
  }

  .badge {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
  }
}

// Dashboard Cards
.dashboard-card {
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  border: none;
  box-shadow: 0 4px 25px rgba(0, 0, 0, 0.1);
  border-radius: 20px;
  min-height: 280px;

  &:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);

    .arrow-icon {
      transform: translateX(5px);
    }

    .stat-box {
      transform: scale(1.05);
    }
  }

  .card-bg-pattern {
    background:
      radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
    opacity: 0.6;
  }

  .card-number {
    font-size: 2.5rem;
    font-weight: 800;
    opacity: 0.3;
    line-height: 1;
  }

  .icon-wrapper {
    .symbol-label {
      border-radius: 16px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }
  }

  .stat-box {
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);

    &:hover {
      background: rgba(255, 255, 255, 0.25) !important;
    }
  }

  .arrow-icon {
    transition: transform 0.3s ease;
    padding: 8px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

// Backdrop blur utility
.backdrop-blur {
  backdrop-filter: blur(10px);
}

// Responsive adjustments
@media (max-width: 768px) {
  .dashboard-card {
    min-height: 240px;

    .card-number {
      font-size: 2rem;
    }

    .card-body {
      padding: 1.5rem !important;
    }
  }

  .dashboard-header {
    padding: 1.5rem !important;

    .col-lg-4 {
      margin-top: 1rem;
      text-align: center !important;
    }
  }
}
