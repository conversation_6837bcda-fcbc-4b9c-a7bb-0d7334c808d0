<!-- Broker Details Content -->
<div class="container-fluid">
  <!-- Header -->
  <div class="row mb-5">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <div class="card-title">
            <div class="d-flex align-items-center">
              <i class="fas fa-user-tie text-primary me-3 fs-2"></i>
              <div>
                <h3 class="mb-0">Broker Details</h3>
                <span class="text-muted fs-6">View comprehensive broker information</span>
              </div>
            </div>
          </div>
          <div class="card-toolbar">
            <button class="btn btn-secondary btn-sm" (click)="goBack()">
              <i class="fas fa-arrow-left me-2"></i>
              Back to Brokers
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Broker Profile Card -->
  <div class="row">
    <div class="col-xl-4">
      <div class="card mb-5 mb-xl-8">
        <div class="card-body pt-15">
          <!-- Profile Image -->
          <div class="d-flex flex-center flex-column mb-5">
            <div class="symbol symbol-100px symbol-circle mb-7">
              <img *ngIf="broker?.image" [src]="broker?.image" alt="User Image" class="w-40 h-30"
                style="object-fit: cover; border-radius: 10%" />
              <div *ngIf="!broker?.image" class="symbol-label bg-light-primary text-primary fw-bold fs-3">
                {{ getInitials(broker?.fullName) }}
              </div>
            </div>

            <!-- Name and Status -->
            <a class="fs-3 text-gray-800 text-hover-primary fw-bold mb-5">
              {{ broker?.fullName }}
            </a>

            <!-- <div class="fs-5 fw-semibold text-muted mb-6">
              {{ broker.specialization || 'Broker' }}
            </div> -->

            <!-- Status Badge -->
            <div class="d-flex flex-wrap fw-semibold fs-6 mb-4 pe-2">
              <span class="badge fs-7 fw-bold px-3 py-2" [ngClass]="getStatusClass(broker?.isActive)">
                {{ getStatusText(broker?.isActive) }}
              </span>
            </div>
          </div>

          <!-- Contact Information -->
          <div class="d-flex flex-stack fs-4 py-3">
            <div class="fw-bold">
              Contact Information
            </div>
          </div>

          <div class="separator separator-dashed my-3"></div>

          <div id="kt_customer_view_details" class="collapse show">
            <div class="py-5 fs-6">
              <!-- Email -->
              <div class="fw-bold mt-5">Email</div>
              <div class="text-gray-600">
                <a [href]="'mailto:' + broker?.email" class="text-gray-600 text-hover-primary">
                  {{ broker?.email }}
                </a>
              </div>

              <!-- Phone -->
              <div class="fw-bold mt-5">Phone</div>
              <div class="text-gray-600">
                <a [href]="'tel:' + broker?.phone" class="text-gray-600 text-hover-primary">
                  {{ broker?.phone }}
                </a>
              </div>

              <!-- Last Login -->
              <div class="fw-bold mt-5" *ngIf="broker?.lastLogin">Last Login</div>
              <div class="text-gray-600" *ngIf="broker?.lastLogin">
                {{ broker?.lastLogin | date:'medium' }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Statistics and Overview -->
    <div class="col-xl-8">
      <!-- Broker Overview -->
      <div class="card mb-5 mb-xl-8">
        <div class="card-header border-0 pt-5">
          <h3 class="card-title align-items-start flex-column">
            <span class="card-label fw-bold fs-3 mb-1">Broker Overview</span>
            <span class="text-muted mt-1 fw-semibold fs-7">Broker's performance statistics</span>
          </h3>
        </div>

        <div class="card-body py-3">
          <div class="row g-6 g-xl-9">
            <!-- Total Advertisements -->
            <div class="col-sm-6 col-xl-4">
              <div class="card h-100">
                <div class="card-body d-flex flex-center flex-column py-9 px-5">
                  <div class="symbol symbol-50px symbol-circle mb-5">
                    <span class="symbol-label bg-light-primary">
                      <i class="fas fa-bullhorn text-primary fs-2x"></i>
                    </span>
                  </div>
                  <div class="fs-4 fw-bold text-gray-800 mb-2">{{ broker?.advertisementCount || 0 }}</div>
                  <div class="fw-semibold text-gray-400">Total Advertisements</div>
                </div>
              </div>
            </div>

            <!-- Active Advertisements -->
            <div class="col-sm-6 col-xl-4">
              <div class="card h-100">
                <div class="card-body d-flex flex-center flex-column py-9 px-5">
                  <div class="symbol symbol-50px symbol-circle mb-5">
                    <span class="symbol-label bg-light-success">
                      <i class="fas fa-check-circle text-success fs-2x"></i>
                    </span>
                  </div>
                  <div class="fs-4 fw-bold text-gray-800 mb-2">{{ broker?.activeAdvertisements || 0 }}</div>
                  <div class="fw-semibold text-gray-400">Active Advertisements</div>
                </div>
              </div>
            </div>

            <!-- Rating -->
            <div class="col-sm-6 col-xl-4">
              <div class="card h-100">
                <div class="card-body d-flex flex-center flex-column py-9 px-5">
                  <div class="symbol symbol-50px symbol-circle mb-5">
                    <span class="symbol-label bg-light-warning">
                      <i class="fas fa-star text-warning fs-2x"></i>
                    </span>
                  </div>
                  <div class="fs-4 fw-bold text-gray-800 mb-2">{{ broker?.avgRating || 'N/A' }}</div>
                  <div class="fw-semibold text-gray-400">Average Rating</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="card">
        <div class="card-header border-0 pt-5">
          <h3 class="card-title align-items-start flex-column">
            <span class="card-label fw-bold fs-3 mb-1">Quick Actions</span>
            <span class="text-muted mt-1 fw-semibold fs-7">Manage broker account</span>
          </h3>
        </div>

        <div class="card-body py-3">
          <div class="d-flex flex-wrap gap-3">
            <a [routerLink]="['/broker/Adds']" [queryParams]="{ brokerId: broker?.brokerId }"
              class="btn btn-primary btn-sm">
              <i class="fas fa-bullhorn me-2"></i>
              View Advertisements
            </a>

            <button class="btn btn-success btn-sm" *ngIf="!broker?.isActive" (click)="toggleBrokerStatus()">
              <i class="fas fa-check me-2"></i>
              Activate Account
            </button>

            <button class="btn btn-warning btn-sm" *ngIf="broker?.isActive" (click)="toggleBrokerStatus()">
              <i class="fas fa-pause me-2"></i>
              Suspend Account
            </button>

            <a class="btn btn-info btn-sm" [routerLink]="['/chat']" [queryParams]="{ chatWithUID: broker?.id }">
              <i class="fas fa-envelope me-2"></i>
              Send Message
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>