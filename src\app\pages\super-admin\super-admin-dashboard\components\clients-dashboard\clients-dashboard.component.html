<div class="container-fluid px-4 py-6">
  <!-- Header -->
  <div class="row mb-6">
    <div class="col-12">
      <div class="d-flex align-items-center justify-content-between">
        <div>
          <h1 class="text-dark-blue fw-bold mb-2">
            <i class="fas fa-users me-3"></i>
            بيانات العملاء
          </h1>
          <p class="text-muted mb-0">إحصائيات شاملة للعملاء والطلبات والسلوك</p>
        </div>
        <button class="btn btn-light-primary" (click)="loadClientStatistics()">
          <i class="fas fa-sync-alt me-2"></i>
          تحديث البيانات
        </button>
      </div>
    </div>
  </div>

  <!-- Main Statistics Cards -->
  <div class="row g-6 mb-8">
    <div class="col-lg-3 col-md-6">
      <div class="card card-flush h-100 bg-success">
        <div class="card-body text-white">
          <div class="d-flex align-items-center">
            <div class="symbol symbol-50px me-3">
              <div class="symbol-label bg-white bg-opacity-20">
                <i class="fas fa-users fs-2 text-white"></i>
              </div>
            </div>
            <div>
              <div class="fs-2 fw-bold">{{ formatNumber(totalClients) }}</div>
              <div class="fs-7 text-white-75">إجمالي العملاء</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-3 col-md-6">
      <div class="card card-flush h-100 bg-dark-blue">
        <div class="card-body text-white">
          <div class="d-flex align-items-center">
            <div class="symbol symbol-50px me-3">
              <div class="symbol-label bg-white bg-opacity-20">
                <i class="fas fa-user-check fs-2 text-white"></i>
              </div>
            </div>
            <div>
              <div class="fs-2 fw-bold">{{ formatNumber(activeClients) }}</div>
              <div class="fs-7 text-white-75">العملاء النشطين</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-3 col-md-6">
      <div class="card card-flush h-100 bg-mid-blue">
        <div class="card-body text-white">
          <div class="d-flex align-items-center">
            <div class="symbol symbol-50px me-3">
              <div class="symbol-label bg-white bg-opacity-20">
                <i class="fas fa-clipboard-list fs-2 text-white"></i>
              </div>
            </div>
            <div>
              <div class="fs-2 fw-bold">{{ formatNumber(totalRequests) }}</div>
              <div class="fs-7 text-white-75">إجمالي الطلبات</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-3 col-md-6">
      <div class="card card-flush h-100 bg-primary">
        <div class="card-body text-white">
          <div class="d-flex align-items-center">
            <div class="symbol symbol-50px me-3">
              <div class="symbol-label bg-white bg-opacity-20">
                <i class="fas fa-clock fs-2 text-white"></i>
              </div>
            </div>
            <div>
              <div class="fs-2 fw-bold">{{ averageResponseTime }}</div>
              <div class="fs-7 text-white-75">متوسط وقت الاستجابة (ساعة)</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Top Clients and Request Stats -->
  <div class="row g-6 mb-8">
    <!-- Top Clients -->
    <div class="col-lg-8">
      <div class="card card-flush h-100">
        <div class="card-header border-0 pt-6">
          <h3 class="card-title text-dark-blue fw-bold">
            <i class="fas fa-star me-2"></i>
            أفضل العملاء
          </h3>
        </div>
        <div class="card-body pt-0">
          <div class="table-responsive">
            <table class="table table-row-bordered align-middle gs-0 gy-3">
              <thead>
                <tr class="fw-bold text-muted bg-light">
                  <th class="ps-4 rounded-start">العميل</th>
                  <th>الطلبات</th>
                  <th>الميزانية الإجمالية</th>
                  <th>المنطقة المفضلة</th>
                  <th class="rounded-end">الحالة</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let client of topClients; let i = index">
                  <td class="ps-4">
                    <div class="d-flex align-items-center">
                      <div class="symbol symbol-40px me-3">
                        <div class="symbol-label bg-light-primary">
                          <span class="text-primary fw-bold">{{ i + 1 }}</span>
                        </div>
                      </div>
                      <div>
                        <span class="text-dark fw-bold d-block">{{ client.name }}</span>
                        <span class="text-muted fs-7">{{ client.phone }}</span>
                      </div>
                    </div>
                  </td>
                  <td>
                    <span class="badge badge-light-primary">{{ client.requests }}</span>
                  </td>
                  <td>
                    <span class="text-success fw-bold">{{ formatPrice(client.totalBudget) }}</span>
                  </td>
                  <td>
                    <span class="text-muted">{{ client.preferredArea }}</span>
                  </td>
                  <td>
                    <span class="badge" [ngClass]="'badge-light-' + getStatusColor(client.status)">
                      {{ client.status }}
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Request Statistics -->
    <div class="col-lg-4">
      <div class="card card-flush h-100">
        <div class="card-header border-0 pt-6">
          <h3 class="card-title text-dark-blue fw-bold">
            <i class="fas fa-chart-pie me-2"></i>
            إحصائيات الطلبات
          </h3>
        </div>
        <div class="card-body pt-0">
          <div class="table-responsive">
            <table class="table table-row-bordered align-middle gs-0 gy-3">
              <thead>
                <tr class="fw-bold text-muted bg-light">
                  <th class="ps-4 rounded-start">الحالة</th>
                  <th>العدد</th>
                  <th class="rounded-end">النسبة</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let request of requestStats">
                  <td class="ps-4">
                    <span class="text-dark fw-bold">{{ request.status }}</span>
                  </td>
                  <td>
                    <span class="badge" [ngClass]="'badge-light-' + getStatusColor(request.status)">
                      {{ request.count }}
                    </span>
                  </td>
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="progress h-6px w-60px me-2">
                        <div class="progress-bar" [ngClass]="'bg-' + getStatusColor(request.status)"
                          [style.width.%]="request.percentage"></div>
                      </div>
                      <span class="text-muted fs-7">{{ request.percentage }}%</span>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Request Type Stats and Budget Range -->
  <div class="row g-6 mb-8">
    <!-- Request Type Statistics -->
    <div class="col-lg-6">
      <div class="card card-flush h-100">
        <div class="card-header border-0 pt-6">
          <h3 class="card-title text-dark-blue fw-bold">
            <i class="fas fa-list me-2"></i>
            أنواع الطلبات
          </h3>
        </div>
        <div class="card-body pt-0">
          <div class="table-responsive">
            <table class="table table-row-bordered align-middle gs-0 gy-3">
              <thead>
                <tr class="fw-bold text-muted bg-light">
                  <th class="ps-4 rounded-start">نوع الطلب</th>
                  <th>العدد</th>
                  <th class="rounded-end">النسبة</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let type of requestTypeStats">
                  <td class="ps-4">
                    <span class="text-dark fw-bold">{{ type.type }}</span>
                  </td>
                  <td>
                    <span class="badge badge-light-primary">{{ type.count }}</span>
                  </td>
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="progress h-6px w-100px me-2">
                        <div class="progress-bar bg-primary" [style.width.%]="type.percentage"></div>
                      </div>
                      <span class="text-muted fs-7">{{ type.percentage }}%</span>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Budget Range Statistics -->
    <div class="col-lg-6">
      <div class="card card-flush h-100">
        <div class="card-header border-0 pt-6">
          <h3 class="card-title text-dark-blue fw-bold">
            <i class="fas fa-money-bill-wave me-2"></i>
            نطاقات الميزانية
          </h3>
        </div>
        <div class="card-body pt-0">
          <div class="table-responsive">
            <table class="table table-row-bordered align-middle gs-0 gy-3">
              <thead>
                <tr class="fw-bold text-muted bg-light">
                  <th class="ps-4 rounded-start">النطاق</th>
                  <th>العدد</th>
                  <th class="rounded-end">النسبة</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let budget of budgetRangeStats">
                  <td class="ps-4">
                    <span class="text-dark fw-bold">{{ budget.range }}</span>
                  </td>
                  <td>
                    <span class="badge badge-light-success">{{ budget.count }}</span>
                  </td>
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="progress h-6px w-100px me-2">
                        <div class="progress-bar bg-success" [style.width.%]="budget.percentage"></div>
                      </div>
                      <span class="text-muted fs-7">{{ budget.percentage }}%</span>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Geographic Distribution and Satisfaction -->
  <div class="row g-6 mb-8">
    <!-- Geographic Distribution -->
    <div class="col-lg-6">
      <div class="card card-flush h-100">
        <div class="card-header border-0 pt-6">
          <h3 class="card-title text-dark-blue fw-bold">
            <i class="fas fa-map-marker-alt me-2"></i>
            التوزيع الجغرافي
          </h3>
        </div>
        <div class="card-body pt-0">
          <div class="table-responsive">
            <table class="table table-row-bordered align-middle gs-0 gy-3">
              <thead>
                <tr class="fw-bold text-muted bg-light">
                  <th class="ps-4 rounded-start">المنطقة</th>
                  <th>العملاء</th>
                  <th class="rounded-end">النسبة</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let area of geographicStats">
                  <td class="ps-4">
                    <span class="text-dark fw-bold">{{ area.area }}</span>
                  </td>
                  <td>
                    <span class="badge badge-light-info">{{ area.clients }}</span>
                  </td>
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="progress h-6px w-100px me-2">
                        <div class="progress-bar bg-info" [style.width.%]="area.percentage"></div>
                      </div>
                      <span class="text-muted fs-7">{{ area.percentage }}%</span>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Customer Satisfaction -->
    <div class="col-lg-6">
      <div class="card card-flush h-100">
        <div class="card-header border-0 pt-6">
          <h3 class="card-title text-dark-blue fw-bold">
            <i class="fas fa-smile me-2"></i>
            رضا العملاء
          </h3>
        </div>
        <div class="card-body pt-0">
          <div class="row g-3">
            <div class="col-6" *ngFor="let key of ['veryHappy', 'happy', 'neutral', 'unhappy', 'veryUnhappy']">
              <div class="p-3 rounded text-center" [ngClass]="'bg-light-' + getSatisfactionColor(key)">
                <div class="fs-6 fw-bold" [ngClass]="'text-' + getSatisfactionColor(key)">
                  {{ getSatisfactionLabel(key) }}
                </div>
                <div class="fs-2 fw-bolder text-dark">{{ satisfactionStats[key] }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Monthly Activity and Client Behavior -->
  <div class="row g-6 mb-8">
    <!-- Monthly Activity -->
    <div class="col-lg-6">
      <div class="card card-flush h-100">
        <div class="card-header border-0 pt-6">
          <h3 class="card-title text-dark-blue fw-bold">
            <i class="fas fa-calendar-alt me-2"></i>
            النشاط الشهري
          </h3>
        </div>
        <div class="card-body pt-0">
          <div class="table-responsive">
            <table class="table table-row-bordered align-middle gs-0 gy-3">
              <thead>
                <tr class="fw-bold text-muted bg-light">
                  <th class="ps-4 rounded-start">الشهر</th>
                  <th>عملاء جدد</th>
                  <th>طلبات</th>
                  <th class="rounded-end">تحويلات</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let month of monthlyActivity">
                  <td class="ps-4">
                    <span class="text-dark fw-bold">{{ month.month }}</span>
                  </td>
                  <td>
                    <span class="badge badge-light-primary">{{ month.newClients }}</span>
                  </td>
                  <td>
                    <span class="badge badge-light-info">{{ month.requests }}</span>
                  </td>
                  <td>
                    <span class="badge badge-light-success">{{ month.conversions }}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Client Behavior -->
    <div class="col-lg-6">
      <div class="card card-flush h-100">
        <div class="card-header border-0 pt-6">
          <h3 class="card-title text-dark-blue fw-bold">
            <i class="fas fa-user-chart me-2"></i>
            سلوك العملاء
          </h3>
        </div>
        <div class="card-body pt-0">
          <div class="table-responsive">
            <table class="table table-row-bordered align-middle gs-0 gy-3">
              <thead>
                <tr class="fw-bold text-muted bg-light">
                  <th class="ps-4 rounded-start">العميل</th>
                  <th>وقت الجلسة</th>
                  <th>الصفحات</th>
                  <th class="rounded-end">التحويل</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let behavior of clientBehavior">
                  <td class="ps-4">
                    <span class="text-dark fw-bold">{{ behavior.name }}</span>
                  </td>
                  <td>
                    <span class="text-muted">{{ behavior.avgSessionTime }} دقيقة</span>
                  </td>
                  <td>
                    <span class="badge badge-light-info">{{ behavior.pagesPerSession }}</span>
                  </td>
                  <td>
                    <span class="badge" [ngClass]="'badge-light-' + getConversionColor(behavior.conversionRate)">
                      {{ behavior.conversionRate }}%
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>