import { Component, OnInit, ChangeDetectorRef } from '@angular/core';

@Component({
  selector: 'app-clients-dashboard',
  templateUrl: './clients-dashboard.component.html',
  styleUrls: ['./clients-dashboard.component.scss']
})
export class ClientsDashboardComponent implements OnInit {

  // Client statistics
  totalClients: number = 0;
  activeClients: number = 0;
  totalRequests: number = 0;
  averageResponseTime: number = 0;

  // Top clients
  topClients: any[] = [];

  // Request statistics
  requestStats: any[] = [];

  // Client behavior
  clientBehavior: any[] = [];

  // Monthly activity
  monthlyActivity: any[] = [];

  // Request type statistics
  requestTypeStats: any[] = [];

  // Client satisfaction
  satisfactionStats: any = {
    veryHappy: 0,
    happy: 0,
    neutral: 0,
    unhappy: 0,
    veryUnhappy: 0
  };

  // Budget range statistics
  budgetRangeStats: any[] = [];

  // Geographic distribution
  geographicStats: any[] = [];

  constructor(private cd: ChangeDetectorRef) {}

  ngOnInit() {
    this.loadClientStatistics();
    this.loadTopClients();
    this.loadRequestStats();
    this.loadClientBehavior();
    this.loadMonthlyActivity();
    this.loadRequestTypeStats();
    this.loadSatisfactionStats();
    this.loadBudgetRangeStats();
    this.loadGeographicStats();
  }

  loadClientStatistics() {
    // Mock data - replace with actual API calls
    this.totalClients = 1085;
    this.activeClients = 890;
    this.totalRequests = 2340;
    this.averageResponseTime = 24; // hours

    this.cd.detectChanges();
  }

  loadTopClients() {
    // Mock data for top clients
    this.topClients = [
      {
        name: 'محمد أحمد السيد',
        phone: '01012345678',
        email: '<EMAIL>',
        requests: 12,
        totalBudget: 15000000,
        joinDate: '2023-01-15',
        status: 'نشط',
        preferredArea: 'القاهرة الجديدة'
      },
      {
        name: 'سارة محمود علي',
        phone: '01098765432',
        email: '<EMAIL>',
        requests: 8,
        totalBudget: 8500000,
        joinDate: '2023-02-20',
        status: 'نشط',
        preferredArea: 'الشيخ زايد'
      },
      {
        name: 'أحمد حسن محمد',
        phone: '01156789012',
        email: '<EMAIL>',
        requests: 10,
        totalBudget: 12000000,
        joinDate: '2022-11-10',
        status: 'نشط',
        preferredArea: 'العاصمة الإدارية'
      },
      {
        name: 'فاطمة عبد الله',
        phone: '01234567890',
        email: '<EMAIL>',
        requests: 6,
        totalBudget: 6500000,
        joinDate: '2023-03-08',
        status: 'نشط',
        preferredArea: 'أكتوبر'
      },
      {
        name: 'عمر خالد أحمد',
        phone: '01087654321',
        email: '<EMAIL>',
        requests: 9,
        totalBudget: 9800000,
        joinDate: '2023-01-14',
        status: 'نشط',
        preferredArea: 'التجمع الخامس'
      }
    ];

    this.cd.detectChanges();
  }

  loadRequestStats() {
    // Mock data for request statistics
    this.requestStats = [
      { status: 'جديد', count: 145, percentage: 25 },
      { status: 'قيد المراجعة', count: 230, percentage: 39 },
      { status: 'مكتمل', count: 180, percentage: 31 },
      { status: 'ملغي', count: 30, percentage: 5 }
    ];

    this.cd.detectChanges();
  }

  loadClientBehavior() {
    // Mock data for client behavior
    this.clientBehavior = [
      {
        name: 'محمد أحمد السيد',
        avgSessionTime: 45, // minutes
        pagesPerSession: 12,
        bounceRate: 25,
        conversionRate: 8.5
      },
      {
        name: 'سارة محمود علي',
        avgSessionTime: 38,
        pagesPerSession: 9,
        bounceRate: 30,
        conversionRate: 6.2
      },
      {
        name: 'أحمد حسن محمد',
        avgSessionTime: 52,
        pagesPerSession: 15,
        bounceRate: 20,
        conversionRate: 9.8
      },
      {
        name: 'فاطمة عبد الله',
        avgSessionTime: 35,
        pagesPerSession: 8,
        bounceRate: 35,
        conversionRate: 5.5
      }
    ];

    this.cd.detectChanges();
  }

  loadMonthlyActivity() {
    // Mock data for monthly activity
    this.monthlyActivity = [
      { month: 'يناير', newClients: 85, requests: 320, conversions: 28 },
      { month: 'فبراير', newClients: 92, requests: 380, conversions: 35 },
      { month: 'مارس', newClients: 78, requests: 290, conversions: 22 },
      { month: 'أبريل', newClients: 105, requests: 420, conversions: 42 },
      { month: 'مايو', newClients: 118, requests: 480, conversions: 48 },
      { month: 'يونيو', newClients: 125, requests: 520, conversions: 55 }
    ];

    this.cd.detectChanges();
  }

  loadRequestTypeStats() {
    // Mock data for request type statistics
    this.requestTypeStats = [
      { type: 'شراء شقة', count: 450, percentage: 38 },
      { type: 'شراء فيلا', count: 280, percentage: 24 },
      { type: 'إيجار شقة', count: 320, percentage: 27 },
      { type: 'إيجار فيلا', count: 95, percentage: 8 },
      { type: 'أخرى', count: 35, percentage: 3 }
    ];

    this.cd.detectChanges();
  }

  loadSatisfactionStats() {
    // Mock data for satisfaction statistics
    this.satisfactionStats = {
      veryHappy: 420,
      happy: 380,
      neutral: 185,
      unhappy: 75,
      veryUnhappy: 25
    };

    this.cd.detectChanges();
  }

  loadBudgetRangeStats() {
    // Mock data for budget range statistics
    this.budgetRangeStats = [
      { range: 'أقل من مليون', count: 125, percentage: 12 },
      { range: '1-3 مليون', count: 380, percentage: 35 },
      { range: '3-5 مليون', count: 290, percentage: 27 },
      { range: '5-10 مليون', count: 210, percentage: 19 },
      { range: 'أكثر من 10 مليون', count: 80, percentage: 7 }
    ];

    this.cd.detectChanges();
  }

  loadGeographicStats() {
    // Mock data for geographic distribution
    this.geographicStats = [
      { area: 'القاهرة الجديدة', clients: 285, percentage: 26 },
      { area: 'الشيخ زايد', clients: 220, percentage: 20 },
      { area: 'العاصمة الإدارية', clients: 195, percentage: 18 },
      { area: 'أكتوبر', clients: 165, percentage: 15 },
      { area: 'التجمع الخامس', clients: 140, percentage: 13 },
      { area: 'أخرى', clients: 80, percentage: 8 }
    ];

    this.cd.detectChanges();
  }

  formatPrice(price: number): string {
    if (price >= 1000000) {
      return (price / 1000000).toFixed(1) + ' مليون جنيه';
    } else if (price >= 1000) {
      return (price / 1000).toFixed(0) + ' ألف جنيه';
    }
    return price.toLocaleString() + ' جنيه';
  }

  formatNumber(num: number): string {
    return num.toLocaleString();
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'جديد': return 'primary';
      case 'قيد المراجعة': return 'warning';
      case 'مكتمل': return 'success';
      case 'ملغي': return 'danger';
      case 'نشط': return 'success';
      default: return 'secondary';
    }
  }

  getPerformanceColor(value: number): string {
    if (value >= 80) return 'success';
    if (value >= 60) return 'primary';
    if (value >= 40) return 'warning';
    return 'danger';
  }

  getSatisfactionColor(type: string): string {
    switch (type) {
      case 'veryHappy': return 'success';
      case 'happy': return 'primary';
      case 'neutral': return 'warning';
      case 'unhappy': return 'danger';
      case 'veryUnhappy': return 'dark';
      default: return 'secondary';
    }
  }

  getSatisfactionLabel(type: string): string {
    switch (type) {
      case 'veryHappy': return 'راضي جداً';
      case 'happy': return 'راضي';
      case 'neutral': return 'محايد';
      case 'unhappy': return 'غير راضي';
      case 'veryUnhappy': return 'غير راضي جداً';
      default: return '';
    }
  }

  getConversionColor(rate: number): string {
    if (rate >= 8) return 'success';
    if (rate >= 6) return 'primary';
    if (rate >= 4) return 'warning';
    return 'danger';
  }
}
