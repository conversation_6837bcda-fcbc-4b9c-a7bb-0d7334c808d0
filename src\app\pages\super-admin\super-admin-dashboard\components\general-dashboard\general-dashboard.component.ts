import { Component, OnInit, ChangeDetectorRef } from '@angular/core';

@Component({
  selector: 'app-general-dashboard',
  templateUrl: './general-dashboard.component.html',
  styleUrls: ['./general-dashboard.component.scss']
})
export class GeneralDashboardComponent implements OnInit {

  // General statistics
  totalUnits: number = 0;
  totalAds: number = 0;
  averagePrice: number = 0;
  totalViews: number = 0;

  // Most demanded units by area
  mostDemandedByArea: any[] = [];
  
  // Most demanded units by type
  mostDemandedByType: any[] = [];
  
  // Best selling units
  bestSellingUnits: any[] = [];
  
  // Price statistics
  priceStatistics: any = {
    averageSalePrice: 0,
    averageRentPrice: 0,
    highestPrice: 0,
    lowestPrice: 0
  };

  // Area statistics
  areaStatistics: any[] = [];

  // Unit type statistics
  unitTypeStatistics: any[] = [];

  constructor(private cd: ChangeDetectorRef) {}

  ngOnInit() {
    this.loadGeneralStatistics();
    this.loadMostDemandedUnits();
    this.loadBestSellingUnits();
    this.loadPriceStatistics();
    this.loadAreaStatistics();
    this.loadUnitTypeStatistics();
  }

  loadGeneralStatistics() {
    // Mock data - replace with actual API calls
    this.totalUnits = 2340;
    this.totalAds = 1890;
    this.averagePrice = 2500000;
    this.totalViews = 45670;

    this.cd.detectChanges();
  }

  loadMostDemandedUnits() {
    // Mock data for most demanded units by area
    this.mostDemandedByArea = [
      { area: 'القاهرة الجديدة', count: 450, percentage: 25 },
      { area: 'الشيخ زايد', count: 380, percentage: 21 },
      { area: 'العاصمة الإدارية', count: 320, percentage: 18 },
      { area: 'أكتوبر', count: 290, percentage: 16 },
      { area: 'التجمع الخامس', count: 260, percentage: 14 }
    ];

    // Mock data for most demanded units by type
    this.mostDemandedByType = [
      { type: 'شقة', count: 890, percentage: 38 },
      { type: 'فيلا', count: 560, percentage: 24 },
      { type: 'دوبلكس', count: 420, percentage: 18 },
      { type: 'بنتهاوس', count: 280, percentage: 12 },
      { type: 'استوديو', count: 190, percentage: 8 }
    ];

    this.cd.detectChanges();
  }

  loadBestSellingUnits() {
    // Mock data for best selling units
    this.bestSellingUnits = [
      { 
        title: 'شقة 3 غرف - القاهرة الجديدة',
        price: 3200000,
        area: 'القاهرة الجديدة',
        type: 'شقة',
        sales: 45,
        developer: 'شركة الأهرام للتطوير'
      },
      { 
        title: 'فيلا 4 غرف - الشيخ زايد',
        price: 5800000,
        area: 'الشيخ زايد',
        type: 'فيلا',
        sales: 38,
        developer: 'شركة إعمار مصر'
      },
      { 
        title: 'دوبلكس 3 غرف - التجمع الخامس',
        price: 4200000,
        area: 'التجمع الخامس',
        type: 'دوبلكس',
        sales: 32,
        developer: 'شركة بالم هيلز'
      },
      { 
        title: 'بنتهاوس 4 غرف - العاصمة الإدارية',
        price: 7500000,
        area: 'العاصمة الإدارية',
        type: 'بنتهاوس',
        sales: 28,
        developer: 'شركة المقاولون العرب'
      }
    ];

    this.cd.detectChanges();
  }

  loadPriceStatistics() {
    // Mock data for price statistics
    this.priceStatistics = {
      averageSalePrice: 3800000,
      averageRentPrice: 15000,
      highestPrice: 12000000,
      lowestPrice: 800000
    };

    this.cd.detectChanges();
  }

  loadAreaStatistics() {
    // Mock data for area statistics
    this.areaStatistics = [
      { name: 'القاهرة الجديدة', units: 450, avgPrice: 3200000, growth: 12 },
      { name: 'الشيخ زايد', units: 380, avgPrice: 4100000, growth: 8 },
      { name: 'العاصمة الإدارية', units: 320, avgPrice: 5200000, growth: 25 },
      { name: 'أكتوبر', units: 290, avgPrice: 2800000, growth: 5 },
      { name: 'التجمع الخامس', units: 260, avgPrice: 3900000, growth: 15 }
    ];

    this.cd.detectChanges();
  }

  loadUnitTypeStatistics() {
    // Mock data for unit type statistics
    this.unitTypeStatistics = [
      { type: 'شقة', count: 890, avgPrice: 2800000, avgArea: 120 },
      { type: 'فيلا', count: 560, avgPrice: 5200000, avgArea: 280 },
      { type: 'دوبلكس', count: 420, avgPrice: 3800000, avgArea: 180 },
      { type: 'بنتهاوس', count: 280, avgPrice: 6500000, avgArea: 220 },
      { type: 'استوديو', count: 190, avgPrice: 1200000, avgArea: 45 }
    ];

    this.cd.detectChanges();
  }

  formatPrice(price: number): string {
    if (price >= 1000000) {
      return (price / 1000000).toFixed(1) + ' مليون جنيه';
    } else if (price >= 1000) {
      return (price / 1000).toFixed(0) + ' ألف جنيه';
    }
    return price.toLocaleString() + ' جنيه';
  }

  formatNumber(num: number): string {
    return num.toLocaleString();
  }
}
