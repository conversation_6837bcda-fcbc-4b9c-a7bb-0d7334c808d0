import { Component, OnInit, ChangeDetectorRef } from '@angular/core';

@Component({
  selector: 'app-brokers-dashboard',
  templateUrl: './brokers-dashboard.component.html',
  styleUrls: ['./brokers-dashboard.component.scss']
})
export class BrokersDashboardComponent implements OnInit {

  // Broker statistics
  totalBrokers: number = 0;
  activeBrokers: number = 0;
  totalCommissions: number = 0;
  averageCommissionRate: number = 0;

  // Top brokers
  topBrokers: any[] = [];
  
  // Commission statistics
  commissionStats: any[] = [];
  
  // Broker performance
  brokerPerformance: any[] = [];

  // Monthly performance
  monthlyPerformance: any[] = [];

  // Specialization statistics
  specializationStats: any[] = [];

  // Transaction statistics
  transactionStats: any = {
    totalTransactions: 0,
    completedTransactions: 0,
    pendingTransactions: 0,
    cancelledTransactions: 0
  };

  // Revenue statistics
  revenueStats: any = {
    totalRevenue: 0,
    monthlyRevenue: 0,
    averageTransactionValue: 0,
    topBrokerRevenue: 0
  };

  constructor(private cd: ChangeDetectorRef) {}

  ngOnInit() {
    this.loadBrokerStatistics();
    this.loadTopBrokers();
    this.loadCommissionStats();
    this.loadBrokerPerformance();
    this.loadMonthlyPerformance();
    this.loadSpecializationStats();
    this.loadTransactionStats();
    this.loadRevenueStats();
  }

  loadBrokerStatistics() {
    // Mock data - replace with actual API calls
    this.totalBrokers = 120;
    this.activeBrokers = 95;
    this.totalCommissions = 45000000;
    this.averageCommissionRate = 2.5; // percentage

    this.cd.detectChanges();
  }

  loadTopBrokers() {
    // Mock data for top brokers
    this.topBrokers = [
      { 
        name: 'أحمد محمد علي',
        phone: '01012345678',
        email: '<EMAIL>',
        transactions: 45,
        totalCommissions: 2800000,
        rating: 4.8,
        specialization: 'شقق سكنية',
        joinDate: '2022-01-15'
      },
      { 
        name: 'سارة أحمد حسن',
        phone: '01098765432',
        email: '<EMAIL>',
        transactions: 38,
        totalCommissions: 2100000,
        rating: 4.7,
        specialization: 'فيلات',
        joinDate: '2022-03-20'
      },
      { 
        name: 'محمد حسام الدين',
        phone: '01156789012',
        email: '<EMAIL>',
        transactions: 42,
        totalCommissions: 2450000,
        rating: 4.6,
        specialization: 'عقارات تجارية',
        joinDate: '2021-11-10'
      },
      { 
        name: 'فاطمة عبد الرحمن',
        phone: '01234567890',
        email: '<EMAIL>',
        transactions: 35,
        totalCommissions: 1950000,
        rating: 4.5,
        specialization: 'شقق سكنية',
        joinDate: '2022-05-08'
      },
      { 
        name: 'عمر خالد محمود',
        phone: '01087654321',
        email: '<EMAIL>',
        transactions: 32,
        totalCommissions: 1750000,
        rating: 4.4,
        specialization: 'دوبلكس',
        joinDate: '2022-02-14'
      }
    ];

    this.cd.detectChanges();
  }

  loadCommissionStats() {
    // Mock data for commission statistics
    this.commissionStats = [
      { range: '1% - 2%', count: 25, percentage: 21 },
      { range: '2% - 3%', count: 45, percentage: 38 },
      { range: '3% - 4%', count: 35, percentage: 29 },
      { range: '4% - 5%', count: 12, percentage: 10 },
      { range: '5%+', count: 3, percentage: 2 }
    ];

    this.cd.detectChanges();
  }

  loadBrokerPerformance() {
    // Mock data for broker performance
    this.brokerPerformance = [
      { 
        name: 'أحمد محمد علي',
        responseTime: 15, // minutes
        clientSatisfaction: 4.8,
        dealClosureRate: 85,
        followUpRate: 92
      },
      { 
        name: 'سارة أحمد حسن',
        responseTime: 12,
        clientSatisfaction: 4.7,
        dealClosureRate: 78,
        followUpRate: 88
      },
      { 
        name: 'محمد حسام الدين',
        responseTime: 18,
        clientSatisfaction: 4.6,
        dealClosureRate: 82,
        followUpRate: 90
      },
      { 
        name: 'فاطمة عبد الرحمن',
        responseTime: 20,
        clientSatisfaction: 4.5,
        dealClosureRate: 75,
        followUpRate: 85
      }
    ];

    this.cd.detectChanges();
  }

  loadMonthlyPerformance() {
    // Mock data for monthly performance
    this.monthlyPerformance = [
      { month: 'يناير', transactions: 145, commissions: 8500000 },
      { month: 'فبراير', transactions: 132, commissions: 7800000 },
      { month: 'مارس', transactions: 168, commissions: 9200000 },
      { month: 'أبريل', transactions: 156, commissions: 8900000 },
      { month: 'مايو', transactions: 174, commissions: 9800000 },
      { month: 'يونيو', transactions: 189, commissions: 10500000 }
    ];

    this.cd.detectChanges();
  }

  loadSpecializationStats() {
    // Mock data for specialization statistics
    this.specializationStats = [
      { specialization: 'شقق سكنية', brokers: 45, percentage: 38 },
      { specialization: 'فيلات', brokers: 28, percentage: 23 },
      { specialization: 'عقارات تجارية', brokers: 22, percentage: 18 },
      { specialization: 'دوبلكس', brokers: 15, percentage: 13 },
      { specialization: 'أراضي', brokers: 10, percentage: 8 }
    ];

    this.cd.detectChanges();
  }

  loadTransactionStats() {
    // Mock data for transaction statistics
    this.transactionStats = {
      totalTransactions: 1245,
      completedTransactions: 980,
      pendingTransactions: 185,
      cancelledTransactions: 80
    };

    this.cd.detectChanges();
  }

  loadRevenueStats() {
    // Mock data for revenue statistics
    this.revenueStats = {
      totalRevenue: 45000000,
      monthlyRevenue: 8500000,
      averageTransactionValue: 3200000,
      topBrokerRevenue: 2800000
    };

    this.cd.detectChanges();
  }

  formatPrice(price: number): string {
    if (price >= 1000000) {
      return (price / 1000000).toFixed(1) + ' مليون جنيه';
    } else if (price >= 1000) {
      return (price / 1000).toFixed(0) + ' ألف جنيه';
    }
    return price.toLocaleString() + ' جنيه';
  }

  formatNumber(num: number): string {
    return num.toLocaleString();
  }

  getPerformanceColor(value: number): string {
    if (value >= 90) return 'success';
    if (value >= 80) return 'primary';
    if (value >= 70) return 'warning';
    return 'danger';
  }

  getRatingStars(rating: number): string[] {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    
    for (let i = 0; i < fullStars; i++) {
      stars.push('fas fa-star text-warning');
    }
    
    if (hasHalfStar) {
      stars.push('fas fa-star-half-alt text-warning');
    }
    
    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push('far fa-star text-muted');
    }
    
    return stars;
  }

  getResponseTimeColor(time: number): string {
    if (time <= 15) return 'success';
    if (time <= 30) return 'warning';
    return 'danger';
  }
}
